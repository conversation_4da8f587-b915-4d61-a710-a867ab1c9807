# 多Tab独立性实现说明

## 概述

本项目实现了完全独立的多Tab对话系统，每个对话Tab都拥有独立的状态，互不影响。当一个Tab出现错误时，不会影响其他Tab的正常运行。

## 核心特性

### 1. 独立状态管理
- 每个Tab都有独立的对话状态
- 侧边栏折叠状态独立
- 输入内容独立保存
- MCP工具配置独立
- 系统提示词独立
- 错误状态独立

### 2. 错误隔离
- 单个Tab的错误不会影响其他Tab
- 每个Tab都有独立的错误处理机制
- 支持错误恢复和重试

### 3. 性能优化
- 状态按需初始化
- Tab关闭时自动清理状态
- 避免不必要的重渲染

## 技术实现

### 状态管理架构

```typescript
// 使用Map存储每个Tab的状态
interface ConversationStore {
  tabStates: Map<string, ConversationTabState>;
  // ... 其他方法
}

// 每个Tab的独立状态
interface ConversationTabState {
  tabId: string;
  loading: boolean;
  error?: string;
  sidebarCollapsed: boolean;
  messages: Message[];
  inputContent: string;
  mcpTools: MCPTool[];
  // ... 其他状态
}
```

### 生命周期管理

```typescript
// Tab生命周期Hook
export const useTabLifecycle = (tabId: string) => {
  const { initializeTab, getTabState } = useConversationStore();

  useEffect(() => {
    initializeTab(tabId); // 自动初始化Tab状态
  }, [tabId, initializeTab]);

  return {
    tabState: getTabState(tabId),
    isInitialized: !!getTabState(tabId),
  };
};
```

### 错误边界

```typescript
// 错误边界Hook
export const useTabErrorBoundary = (tabId: string) => {
  const { getTabState, setError } = useConversationStore();
  
  return {
    error: tabState?.error,
    clearError: () => setError(tabId, undefined),
    setTabError: (error: string) => setError(tabId, error),
  };
};
```

## 使用方式

### 1. 创建多个Tab进行测试

使用标题栏的"+"按钮创建新的对话Tab，或者使用欢迎页面的"开始对话"按钮。

### 2. 测试独立性

1. **侧边栏状态测试**：
   - 在Tab A中折叠侧边栏
   - 切换到Tab B，侧边栏应该保持展开状态
   - 切换回Tab A，侧边栏应该保持折叠状态

2. **输入内容测试**：
   - 在Tab A中输入一些文字但不发送
   - 切换到Tab B，输入框应该为空
   - 切换回Tab A，之前输入的文字应该还在

3. **MCP工具配置测试**：
   - 在Tab A中启用某些MCP工具
   - 切换到Tab B，MCP工具配置应该是默认状态
   - 在Tab B中配置不同的工具
   - 切换回Tab A，应该保持之前的配置

4. **对话历史测试**：
   - 在Tab A中发送消息
   - 切换到Tab B，对话历史应该为空
   - 在Tab B中发送消息
   - 切换回Tab A，应该只显示Tab A的对话历史

5. **错误处理测试**：
   - 在Tab A中触发错误（发送消息时有20%概率失败）
   - Tab A显示错误信息
   - 切换到Tab B，应该没有错误信息
   - Tab B的功能应该正常工作

## 文件结构

```
src/
├── stores/
│   ├── useConversationStore.ts  # 对话状态管理
│   └── useTabStore.ts          # Tab管理
├── hooks/
│   └── useTabLifecycle.ts      # Tab生命周期管理
├── components/
│   ├── ConversationTabContent.tsx  # 对话Tab内容
│   └── TabContent.tsx             # Tab内容路由
└── types/
    └── index.ts                  # 类型定义
```

## 核心优势

1. **完全隔离**：每个Tab的状态完全独立，不会相互影响
2. **错误容错**：单个Tab的错误不会影响整个应用
3. **用户体验**：用户可以在多个对话之间自由切换，每个对话都保持其状态
4. **可扩展性**：易于添加新的Tab类型和状态
5. **性能优化**：按需加载和清理，避免内存泄漏

## 测试建议

访问应用后：

1. 使用标题栏的"+"按钮或欢迎页面的"开始对话"按钮创建多个Tab
2. 按照上述测试方法验证各项独立性功能
3. 观察Tab切换时状态的保持情况
4. 测试错误处理的隔离效果

这种设计确保了多Tab环境下的稳定性和用户体验，每个Tab都是一个独立的对话环境。 