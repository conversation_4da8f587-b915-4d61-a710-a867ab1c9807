import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { User, AppState } from '@/types';

interface AppStore extends AppState {
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | undefined) => void;
  setUser: (user: User | undefined) => void;
  setAuthenticated: (authenticated: boolean) => void;
  login: (user: User) => void;
  logout: () => void;
  clearError: () => void;
  
  // UI相关状态
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  
  // 主题相关
  isDarkMode: boolean;
  setDarkMode: (darkMode: boolean) => void;
  toggleDarkMode: () => void;
}

const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        isLoading: false,
        error: undefined,
        isAuthenticated: false,
        user: undefined,
        sidebarCollapsed: false,
        isDarkMode: false,
        
        // Actions实现
        setLoading: (loading) => {
          set({ isLoading: loading });
        },
        
        setError: (error) => {
          set({ error });
        },
        
        clearError: () => {
          set({ error: undefined });
        },
        
        setUser: (user) => {
          set({ user });
        },
        
        setAuthenticated: (authenticated) => {
          set({ isAuthenticated: authenticated });
        },
        
        login: (user) => {
          set({
            user,
            isAuthenticated: true,
            error: undefined,
          });
        },
        
        logout: () => {
          set({
            user: undefined,
            isAuthenticated: false,
            error: undefined,
          });
        },
        
        setSidebarCollapsed: (collapsed) => {
          set({ sidebarCollapsed: collapsed });
        },
        
        setDarkMode: (darkMode) => {
          set({ isDarkMode: darkMode });
          
          // 应用到DOM
          if (typeof window !== 'undefined') {
            if (darkMode) {
              document.documentElement.classList.add('dark');
            } else {
              document.documentElement.classList.remove('dark');
            }
          }
        },
        
        toggleDarkMode: () => {
          const newDarkMode = !get().isDarkMode;
          get().setDarkMode(newDarkMode);
        },
      }),
      {
        name: 'app-store',
        // 选择性持久化
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          sidebarCollapsed: state.sidebarCollapsed,
          isDarkMode: state.isDarkMode,
        }),
      }
    ),
    {
      name: 'app-store',
    }
  )
);

export default useAppStore; 