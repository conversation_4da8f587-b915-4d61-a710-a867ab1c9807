import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { ModelProvider, Model, GeneralConfig, MCPConfig, PromptConfig } from '@/types';

interface ConfigStore {
  // 服务商配置
  providers: ModelProvider[];
  selectedProviderId: string | null;
  
  // 通用配置
  generalConfig: GeneralConfig;
  
  // MCP配置
  mcpConfigs: MCPConfig[];
  
  // Prompt配置
  promptConfigs: PromptConfig[];
  
  // Actions
  setProviders: (providers: ModelProvider[]) => void;
  updateProvider: (providerId: string, updates: Partial<ModelProvider>) => void;
  toggleProvider: (providerId: string) => void;
  setSelectedProvider: (providerId: string | null) => void;
  updateProviderApiKey: (providerId: string, apiKey: string) => void;
  updateProviderApiUrl: (providerId: string, apiUrl: string) => void;
  toggleProviderModel: (providerId: string, modelId: string) => void;
  enableAllModels: (providerId: string) => void;
  disableAllModels: (providerId: string) => void;
  validateApiKey: (providerId: string) => Promise<boolean>;
  
  // 通用配置Actions
  updateGeneralConfig: (config: Partial<GeneralConfig>) => void;
  
  // MCP配置Actions
  addMCPConfig: (config: MCPConfig) => void;
  updateMCPConfig: (id: string, config: Partial<MCPConfig>) => void;
  removeMCPConfig: (id: string) => void;
  
  // Prompt配置Actions
  addPromptConfig: (config: PromptConfig) => void;
  updatePromptConfig: (id: string, config: Partial<PromptConfig>) => void;
  removePromptConfig: (id: string) => void;
}

// 默认服务商数据
const defaultProviders: ModelProvider[] = [
  {
    id: 'ollama',
    name: 'Ollama',
    enabled: false,
    apiUrl: 'http://localhost:11434',
    apiKey: '',
    defaultApiUrl: 'http://localhost:11434',
    models: [],
    keyValidated: false,
  },
  {
    id: 'deepseek',
    name: 'Deepseek',
    enabled: false,
    apiUrl: 'https://api.deepseek.com',
    apiKey: '',
    defaultApiUrl: 'https://api.deepseek.com',
    models: [],
    keyValidated: false,
  },
  {
    id: 'qiniu',
    name: '七牛云',
    enabled: false,
    apiUrl: 'https://api.qiniu.com',
    apiKey: '',
    defaultApiUrl: 'https://api.qiniu.com',
    models: [],
    keyValidated: false,
  },
  {
    id: 'siliconflow',
    name: '硅基流动',
    enabled: false,
    apiUrl: 'https://api.siliconflow.cn',
    apiKey: '',
    defaultApiUrl: 'https://api.siliconflow.cn',
    models: [],
    keyValidated: false,
  },
  {
    id: 'doubao',
    name: '豆包',
    enabled: false,
    apiUrl: 'https://api.doubao.com',
    apiKey: '',
    defaultApiUrl: 'https://api.doubao.com',
    models: [],
    keyValidated: false,
  },
  {
    id: 'minimax',
    name: 'MiniMax',
    enabled: false,
    apiUrl: 'https://api.minimax.chat',
    apiKey: '',
    defaultApiUrl: 'https://api.minimax.chat',
    models: [],
    keyValidated: false,
  },
  {
    id: 'fireworks',
    name: 'Fireworks',
    enabled: false,
    apiUrl: 'https://api.fireworks.ai',
    apiKey: '',
    defaultApiUrl: 'https://api.fireworks.ai',
    models: [],
    keyValidated: false,
  },
  {
    id: 'paimeng',
    name: '派蒙云',
    enabled: false,
    apiUrl: 'https://api.paimeng.com',
    apiKey: '',
    defaultApiUrl: 'https://api.paimeng.com',
    models: [],
    keyValidated: false,
  },
  {
    id: 'openai-responses',
    name: 'OpenAI Responses',
    enabled: false,
    apiUrl: 'https://api.openai.com/v1',
    apiKey: '',
    defaultApiUrl: 'https://api.openai.com/v1',
    models: [],
    keyValidated: false,
  },
  {
    id: 'openai',
    name: 'OpenAI',
    enabled: true,
    apiUrl: 'https://api-inference.modelscope.cn/v1/',
    apiKey: '',
    defaultApiUrl: 'https://api.openai.com/v1',
    models: [
      {
        id: 'llm-research-c4ai-command-r-plus-08-2024',
        name: 'LLM-Research/c4ai-command-r-plus-08-2024',
        provider: 'openai',
        enabled: true,
      },
      {
        id: 'mistralai-mistral-small-instruct-2409',
        name: 'mistralai/Mistral-Small-Instruct-2409',
        provider: 'openai',
        enabled: true,
      },
      {
        id: 'mistralai-ministral-8b-instruct-2410',
        name: 'mistralai/Ministral-8B-Instruct-2410',
        provider: 'openai',
        enabled: true,
      },
      {
        id: 'mistralai-mistral-large-instruct-2407',
        name: 'mistralai/Mistral-Large-Instruct-2407',
        provider: 'openai',
        enabled: true,
      },
    ],
    keyValidated: false,
  },
  {
    id: 'gemini',
    name: 'Gemini',
    enabled: false,
    apiUrl: 'https://generativelanguage.googleapis.com',
    apiKey: '',
    defaultApiUrl: 'https://generativelanguage.googleapis.com',
    models: [],
    keyValidated: false,
  },
  {
    id: 'github-models',
    name: 'Github Models',
    enabled: false,
    apiUrl: 'https://models.inference.ai.azure.com',
    apiKey: '',
    defaultApiUrl: 'https://models.inference.ai.azure.com',
    models: [],
    keyValidated: false,
  },
];

const useConfigStore = create<ConfigStore>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        providers: defaultProviders,
        selectedProviderId: 'openai',
        generalConfig: {
          searchEngine: 'google',
          darkMode: false,
          language: 'zh',
        },
        mcpConfigs: [],
        promptConfigs: [],
        
        // 服务商相关Actions
        setProviders: (providers) => {
          set({ providers });
        },
        
        updateProvider: (providerId, updates) => {
          set((state) => ({
            providers: state.providers.map((provider) =>
              provider.id === providerId ? { ...provider, ...updates } : provider
            ),
          }));
        },
        
        toggleProvider: (providerId) => {
          set((state) => ({
            providers: state.providers.map((provider) =>
              provider.id === providerId
                ? { ...provider, enabled: !provider.enabled }
                : provider
            ),
          }));
        },
        
        setSelectedProvider: (providerId) => {
          set({ selectedProviderId: providerId });
        },
        
        updateProviderApiKey: (providerId, apiKey) => {
          get().updateProvider(providerId, { apiKey, keyValidated: false });
        },
        
        updateProviderApiUrl: (providerId, apiUrl) => {
          get().updateProvider(providerId, { apiUrl });
        },
        
        toggleProviderModel: (providerId, modelId) => {
          set((state) => ({
            providers: state.providers.map((provider) =>
              provider.id === providerId
                ? {
                    ...provider,
                    models: provider.models.map((model) =>
                      model.id === modelId
                        ? { ...model, enabled: !model.enabled }
                        : model
                    ),
                  }
                : provider
            ),
          }));
        },
        
        enableAllModels: (providerId) => {
          set((state) => ({
            providers: state.providers.map((provider) =>
              provider.id === providerId
                ? {
                    ...provider,
                    models: provider.models.map((model) => ({
                      ...model,
                      enabled: true,
                    })),
                  }
                : provider
            ),
          }));
        },
        
        disableAllModels: (providerId) => {
          set((state) => ({
            providers: state.providers.map((provider) =>
              provider.id === providerId
                ? {
                    ...provider,
                    models: provider.models.map((model) => ({
                      ...model,
                      enabled: false,
                    })),
                  }
                : provider
            ),
          }));
        },
        
        validateApiKey: async (providerId) => {
          // 模拟API密钥验证
          await new Promise((resolve) => setTimeout(resolve, 1000));
          const isValid = Math.random() > 0.3; // 70%概率验证成功
          
          get().updateProvider(providerId, { keyValidated: isValid });
          return isValid;
        },
        
        // 通用配置Actions
        updateGeneralConfig: (config) => {
          set((state) => ({
            generalConfig: { ...state.generalConfig, ...config },
          }));
        },
        
        // MCP配置Actions
        addMCPConfig: (config) => {
          set((state) => ({
            mcpConfigs: [...state.mcpConfigs, config],
          }));
        },
        
        updateMCPConfig: (id, config) => {
          set((state) => ({
            mcpConfigs: state.mcpConfigs.map((mcp) =>
              mcp.id === id ? { ...mcp, ...config } : mcp
            ),
          }));
        },
        
        removeMCPConfig: (id) => {
          set((state) => ({
            mcpConfigs: state.mcpConfigs.filter((mcp) => mcp.id !== id),
          }));
        },
        
        // Prompt配置Actions
        addPromptConfig: (config) => {
          set((state) => ({
            promptConfigs: [...state.promptConfigs, config],
          }));
        },
        
        updatePromptConfig: (id, config) => {
          set((state) => ({
            promptConfigs: state.promptConfigs.map((prompt) =>
              prompt.id === id ? { ...prompt, ...config } : prompt
            ),
          }));
        },
        
        removePromptConfig: (id) => {
          set((state) => ({
            promptConfigs: state.promptConfigs.filter((prompt) => prompt.id !== id),
          }));
        },
      }),
      {
        name: 'config-store',
        // 选择性持久化
        partialize: (state) => ({
          providers: state.providers,
          selectedProviderId: state.selectedProviderId,
          generalConfig: state.generalConfig,
          mcpConfigs: state.mcpConfigs,
          promptConfigs: state.promptConfigs,
        }),
      }
    ),
    {
      name: 'config-store',
    }
  )
);

export default useConfigStore;
