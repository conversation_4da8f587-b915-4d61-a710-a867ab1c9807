import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Message, Conversation } from '@/types';

/**
 * 对话历史项接口
 */
interface ConversationHistoryItem {
  id: string;
  title: string;
  active: boolean;
  lastMessageTime?: Date;
  messageCount?: number;
}

/**
 * MCP工具配置接口
 */
interface MCPTool {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  icon?: string;
}

/**
 * 单个对话tab的状态接口
 */
interface ConversationTabState {
  // 基础状态
  tabId: string;
  loading: boolean;
  error?: string;
  
  // 对话相关
  currentConversationId?: string;
  messages: Message[];
  inputContent: string;
  isTyping: boolean;
  
  // 历史对话列表
  conversations: ConversationHistoryItem[];
  
  // UI状态
  sidebarCollapsed: boolean;
  hoveredItem: string | null;
  showMenu: string | null;
  showToolMenu: boolean;
  showSettingsMenu: boolean;
  
  // 设置
  systemPrompt: string;
  selectedModel: string;
  mcpTools: MCPTool[];
  
  // 文件上传相关
  attachments: File[];
  isDragOver: boolean;
}

/**
 * 对话store接口
 */
interface ConversationStore {
  // 所有tab的状态映射
  tabStates: Map<string, ConversationTabState>;
  
  // Actions
  initializeTab: (tabId: string) => void;
  removeTab: (tabId: string) => void;
  
  // 消息相关操作
  addMessage: (tabId: string, message: Omit<Message, 'id' | 'timestamp'>) => void;
  updateMessage: (tabId: string, messageId: string, updates: Partial<Message>) => void;
  deleteMessage: (tabId: string, messageId: string) => void;
  clearMessages: (tabId: string) => void;
  
  // 对话历史操作
  addConversation: (tabId: string, conversation: Omit<ConversationHistoryItem, 'id'>) => void;
  updateConversation: (tabId: string, conversationId: string, updates: Partial<ConversationHistoryItem>) => void;
  deleteConversation: (tabId: string, conversationId: string) => void;
  setActiveConversation: (tabId: string, conversationId: string) => void;
  
  // UI状态操作
  setSidebarCollapsed: (tabId: string, collapsed: boolean) => void;
  setHoveredItem: (tabId: string, itemId: string | null) => void;
  setShowMenu: (tabId: string, menuId: string | null) => void;
  setShowToolMenu: (tabId: string, show: boolean) => void;
  setShowSettingsMenu: (tabId: string, show: boolean) => void;
  
  // 输入相关操作
  setInputContent: (tabId: string, content: string) => void;
  setIsTyping: (tabId: string, typing: boolean) => void;
  
  // 设置操作
  setSystemPrompt: (tabId: string, prompt: string) => void;
  setSelectedModel: (tabId: string, model: string) => void;
  updateMCPTool: (tabId: string, toolId: string, updates: Partial<MCPTool>) => void;
  
  // 文件操作
  addAttachment: (tabId: string, file: File) => void;
  removeAttachment: (tabId: string, fileIndex: number) => void;
  clearAttachments: (tabId: string) => void;
  setIsDragOver: (tabId: string, isDragOver: boolean) => void;
  
  // 加载状态
  setLoading: (tabId: string, loading: boolean) => void;
  setError: (tabId: string, error?: string) => void;
  
  // 获取器
  getTabState: (tabId: string) => ConversationTabState | undefined;
}

/**
 * 创建默认的tab状态
 */
const createDefaultTabState = (tabId: string): ConversationTabState => ({
  tabId,
  loading: false,
  error: undefined,
  
  // 对话相关
  currentConversationId: undefined,
  messages: [],
  inputContent: '',
  isTyping: false,
  
  // 历史对话列表（示例数据）
  conversations: [
    { 
      id: `${tabId}-conv-1`, 
      title: '大型语言模型功能介绍', 
      active: true,
      lastMessageTime: new Date('2025-05-24T14:52:00'),
      messageCount: 2
    },
    { 
      id: `${tabId}-conv-2`, 
      title: '大型语言模型功能介绍', 
      active: false,
      lastMessageTime: new Date('2025-05-24T14:50:00'),
      messageCount: 5
    },
  ],
  
  // UI状态
  sidebarCollapsed: false,
  hoveredItem: null,
  showMenu: null,
  showToolMenu: false,
  showSettingsMenu: false,
  
  // 设置
  systemPrompt: '',
  selectedModel: 'Qwen/QwQ-32B-Preview',
  mcpTools: [
    { id: 'mcp-enable', name: '启用MCP', description: '启用MCP功能以使用工具调用', enabled: true },
    { id: 'artifacts', name: 'Artifacts', description: '', enabled: true, icon: '🍪' },
    { id: 'filesystem', name: 'buildInFileSystem', description: '', enabled: false, icon: '📁' },
    { id: 'search', name: 'bochaSearch', description: '', enabled: false, icon: '🔍' },
    { id: 'brave', name: 'braveSearch', description: '', enabled: false, icon: '🦁' },
    { id: 'knowledge', name: 'difyKnowledge', description: '', enabled: false, icon: '📚' },
    { id: 'image', name: 'imageServer', description: '', enabled: false, icon: '🖼️' },
    { id: 'powerpack', name: 'powerpack', description: '', enabled: false, icon: '🔧' },
  ],
  
  // 文件上传相关
  attachments: [],
  isDragOver: false,
});

/**
 * 对话状态管理store
 */
const useConversationStore = create<ConversationStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      tabStates: new Map(),
      
      // Actions实现
      initializeTab: (tabId) => {
        const state = get();
        if (!state.tabStates.has(tabId)) {
          const newTabStates = new Map(state.tabStates);
          newTabStates.set(tabId, createDefaultTabState(tabId));
          set({ tabStates: newTabStates });
        }
      },
      
      removeTab: (tabId) => {
        const state = get();
        const newTabStates = new Map(state.tabStates);
        newTabStates.delete(tabId);
        set({ tabStates: newTabStates });
      },
      
      // 消息相关操作
      addMessage: (tabId, messageData) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newMessage: Message = {
          ...messageData,
          id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          timestamp: new Date(),
        };
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          messages: [...tabState.messages, newMessage],
        });
        set({ tabStates: newTabStates });
      },
      
      updateMessage: (tabId, messageId, updates) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          messages: tabState.messages.map(msg =>
            msg.id === messageId ? { ...msg, ...updates } : msg
          ),
        });
        set({ tabStates: newTabStates });
      },
      
      deleteMessage: (tabId, messageId) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          messages: tabState.messages.filter(msg => msg.id !== messageId),
        });
        set({ tabStates: newTabStates });
      },
      
      clearMessages: (tabId) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          messages: [],
        });
        set({ tabStates: newTabStates });
      },
      
      // 对话历史操作
      addConversation: (tabId, conversationData) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newConversation: ConversationHistoryItem = {
          ...conversationData,
          id: `conv-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        };
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          conversations: [newConversation, ...tabState.conversations],
        });
        set({ tabStates: newTabStates });
      },
      
      updateConversation: (tabId, conversationId, updates) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          conversations: tabState.conversations.map(conv =>
            conv.id === conversationId ? { ...conv, ...updates } : conv
          ),
        });
        set({ tabStates: newTabStates });
      },
      
      deleteConversation: (tabId, conversationId) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          conversations: tabState.conversations.filter(conv => conv.id !== conversationId),
        });
        set({ tabStates: newTabStates });
      },
      
      setActiveConversation: (tabId, conversationId) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          currentConversationId: conversationId,
          conversations: tabState.conversations.map(conv => ({
            ...conv,
            active: conv.id === conversationId,
          })),
        });
        set({ tabStates: newTabStates });
      },
      
      // UI状态操作
      setSidebarCollapsed: (tabId, collapsed) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          sidebarCollapsed: collapsed,
        });
        set({ tabStates: newTabStates });
      },
      
      setHoveredItem: (tabId, itemId) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          hoveredItem: itemId,
        });
        set({ tabStates: newTabStates });
      },
      
      setShowMenu: (tabId, menuId) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          showMenu: menuId,
        });
        set({ tabStates: newTabStates });
      },
      
      setShowToolMenu: (tabId, show) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          showToolMenu: show,
        });
        set({ tabStates: newTabStates });
      },
      
      setShowSettingsMenu: (tabId, show) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          showSettingsMenu: show,
        });
        set({ tabStates: newTabStates });
      },
      
      // 输入相关操作
      setInputContent: (tabId, content) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          inputContent: content,
        });
        set({ tabStates: newTabStates });
      },
      
      setIsTyping: (tabId, typing) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          isTyping: typing,
        });
        set({ tabStates: newTabStates });
      },
      
      // 设置操作
      setSystemPrompt: (tabId, prompt) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          systemPrompt: prompt,
        });
        set({ tabStates: newTabStates });
      },
      
      setSelectedModel: (tabId, model) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          selectedModel: model,
        });
        set({ tabStates: newTabStates });
      },
      
      updateMCPTool: (tabId, toolId, updates) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          mcpTools: tabState.mcpTools.map(tool =>
            tool.id === toolId ? { ...tool, ...updates } : tool
          ),
        });
        set({ tabStates: newTabStates });
      },
      
      // 文件操作
      addAttachment: (tabId, file) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          attachments: [...tabState.attachments, file],
        });
        set({ tabStates: newTabStates });
      },
      
      removeAttachment: (tabId, fileIndex) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          attachments: tabState.attachments.filter((_, index) => index !== fileIndex),
        });
        set({ tabStates: newTabStates });
      },
      
      clearAttachments: (tabId) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          attachments: [],
        });
        set({ tabStates: newTabStates });
      },
      
      setIsDragOver: (tabId, isDragOver) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          isDragOver,
        });
        set({ tabStates: newTabStates });
      },
      
      // 加载状态
      setLoading: (tabId, loading) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          loading,
        });
        set({ tabStates: newTabStates });
      },
      
      setError: (tabId, error) => {
        const state = get();
        const tabState = state.tabStates.get(tabId);
        if (!tabState) return;
        
        const newTabStates = new Map(state.tabStates);
        newTabStates.set(tabId, {
          ...tabState,
          error,
        });
        set({ tabStates: newTabStates });
      },
      
      // 获取器
      getTabState: (tabId) => {
        return get().tabStates.get(tabId);
      },
    }),
    {
      name: 'conversation-store',
    }
  )
);

export default useConversationStore; 