import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Tab, TabType } from '@/types';

interface TabStore {
  // 状态
  tabs: Tab[];
  activeTabId: string | null;
  
  // Actions
  addTab: (tab: Omit<Tab, 'id' | 'isActive'>) => string;
  removeTab: (tabId: string) => void;
  setActiveTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<Tab>) => void;
  closeTab: (tabId: string) => void;
  createConversationTab: (title?: string) => string;
  createConfigurationTab: (title?: string) => string;
  moveTab: (fromIndex: number, toIndex: number) => void;
  
  // 计算属性
  getActiveTab: () => Tab | null;
  getTabsByType: (type: TabType) => Tab[];
}

const useTabStore = create<TabStore>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        tabs: [],
        activeTabId: null,
        
        // Actions实现
        addTab: (tabData) => {
          const newId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
          const newTab: Tab = {
            ...tabData,
            id: newId,
            isActive: false,
          };
          
          set((state) => ({
            tabs: [...state.tabs, newTab],
          }));
          
          // 如果没有激活的Tab，自动激活新创建的Tab
          if (!get().activeTabId) {
            get().setActiveTab(newId);
          }
          
          return newId;
        },
        
        removeTab: (tabId) => {
          const state = get();
          const tabIndex = state.tabs.findIndex(tab => tab.id === tabId);
          
          if (tabIndex === -1) return;
          
          const isActiveTab = state.activeTabId === tabId;
          const newTabs = state.tabs.filter(tab => tab.id !== tabId);
          
          let newActiveTabId = state.activeTabId;
          
          // 如果删除的是激活Tab，需要选择新的激活Tab
          if (isActiveTab && newTabs.length > 0) {
            // 优先选择右侧的Tab，如果没有则选择左侧
            const nextIndex = tabIndex < newTabs.length ? tabIndex : newTabs.length - 1;
            newActiveTabId = newTabs[nextIndex]?.id || null;
          } else if (newTabs.length === 0) {
            newActiveTabId = null;
          }
          
          set({
            tabs: newTabs,
            activeTabId: newActiveTabId,
          });
        },
        
        setActiveTab: (tabId) => {
          const state = get();
          if (!state.tabs.find(tab => tab.id === tabId)) return;
          
          set((state) => ({
            activeTabId: tabId,
            tabs: state.tabs.map(tab => ({
              ...tab,
              isActive: tab.id === tabId,
            })),
          }));
        },
        
        updateTab: (tabId, updates) => {
          set((state) => ({
            tabs: state.tabs.map(tab =>
              tab.id === tabId ? { ...tab, ...updates } : tab
            ),
          }));
        },
        
        closeTab: (tabId) => {
          const tab = get().tabs.find(t => t.id === tabId);
          if (tab?.canClose !== false) {
            get().removeTab(tabId);
          }
        },
        
        createConversationTab: (title = '新对话') => {
          // 为每个对话创建唯一标题
          const state = get();
          const conversationTabs = state.tabs.filter(tab => tab.type === TabType.CONVERSATION);
          const newTitle = title === '新对话' ? `新对话 ${conversationTabs.length + 1}` : title;
          
          const newTabId = get().addTab({
            title: newTitle,
            type: TabType.CONVERSATION,
            canClose: true,
            content: {
              conversationId: null, // 新对话暂时没有ID
            },
          });
          
          // 自动激活新创建的对话Tab
          get().setActiveTab(newTabId);
          return newTabId;
        },
        
        createConfigurationTab: (title = '配置') => {
          const state = get();
          
          // 检查是否已存在配置Tab
          const existingConfigTab = state.tabs.find(tab => tab.type === TabType.CONFIGURATION);
          if (existingConfigTab) {
            // 如果已存在，直接激活它
            get().setActiveTab(existingConfigTab.id);
            return existingConfigTab.id;
          }
          
          // 创建新的配置Tab
          const newTabId = get().addTab({
            title,
            type: TabType.CONFIGURATION,
            canClose: true,
            content: {
              activeSection: 'general', // 默认显示通用配置
            },
          });
          
          // 自动激活新创建的配置Tab
          get().setActiveTab(newTabId);
          return newTabId;
        },
        
        moveTab: (fromIndex, toIndex) => {
          set((state) => {
            const newTabs = [...state.tabs];
            const [movedTab] = newTabs.splice(fromIndex, 1);
            newTabs.splice(toIndex, 0, movedTab);
            return { tabs: newTabs };
          });
        },
        
        // 计算属性
        getActiveTab: () => {
          const state = get();
          return state.tabs.find(tab => tab.id === state.activeTabId) || null;
        },
        
        getTabsByType: (type) => {
          return get().tabs.filter(tab => tab.type === type);
        },
      }),
      {
        name: 'tab-store',
        // 只持久化基本信息，不持久化内容
        partialize: (state) => ({
          tabs: state.tabs.map(tab => ({
            ...tab,
            content: undefined, // 不持久化内容，避免数据过大
          })),
          activeTabId: state.activeTabId,
        }),
      }
    ),
    {
      name: 'tab-store',
    }
  )
);

export default useTabStore; 