// 基础类型定义

// Tab 类型枚举
export enum TabType {
  CONVERSATION = 'conversation',
  CONFIGURATION = 'configuration'
}

// Tab 接口
export interface Tab {
  id: string;
  title: string;
  type: TabType;
  isActive: boolean;
  canClose: boolean;
  content?: any;
}

// 对话类型
export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'error';
  attachments?: Attachment[];
}

export interface Attachment {
  id: string;
  name: string;
  type: 'image' | 'file' | 'document';
  size: number;
  url: string;
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  model: string;
  createdAt: Date;
  updatedAt: Date;
}

// 模型相关类型
export interface Model {
  id: string;
  name: string;
  provider: string;
  enabled: boolean;
}

export interface ModelProvider {
  id: string;
  name: string;
  enabled: boolean;
  apiUrl: string;
  apiKey: string;
  models: Model[];
}

// 配置类型
export interface GeneralConfig {
  searchEngine: 'baidu' | 'google';
  darkMode: boolean;
  language: 'zh' | 'en';
}

export interface MCPConfig {
  id: string;
  name: string;
  type: 'stdio' | 'sse';
  command?: string;
  args?: string[];
  url?: string;
  enabled: boolean;
}

export interface PromptConfig {
  id: string;
  name: string;
  content: string;
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

// 用户类型
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

// 应用状态类型
export interface AppState {
  isLoading: boolean;
  error?: string;
  isAuthenticated: boolean;
  user?: User;
} 