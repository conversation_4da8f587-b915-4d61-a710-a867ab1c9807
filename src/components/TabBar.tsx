'use client';

import React, { useState, useEffect, useRef } from 'react';
import useTabStore from '@/stores/useTabStore';
import { Tab, TabType } from '@/types';

/**
 * Tab栏组件
 * 显示所有打开的Tab，支持切换、关闭和添加新Tab
 */
const TabBar: React.FC = () => {
  const {
    tabs,
    activeTabId,
    setActiveTab,
    closeTab,
    createConversationTab,
    createConfigurationTab,
  } = useTabStore();

  const [isOverflowing, setIsOverflowing] = useState(false);
  const tabContainerRef = useRef<HTMLDivElement>(null);
  const [shouldScrollToNew, setShouldScrollToNew] = useState(false);

  // 检测Tab容器是否溢出，需要滚动
  useEffect(() => {
    const checkOverflow = () => {
      if (tabContainerRef.current) {
        const { scrollWidth, clientWidth } = tabContainerRef.current;
        setIsOverflowing(scrollWidth > clientWidth + 10); // 添加10px容差，考虑padding
      }
    };

    // 使用requestAnimationFrame确保DOM更新完成后再检测
    const timeoutId = setTimeout(() => {
      checkOverflow();
    }, 10);

    const handleResize = () => {
      checkOverflow();
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
    };
  }, [tabs]);

  // 当需要滚动到新Tab时，滚动到最右侧
  useEffect(() => {
    if (shouldScrollToNew && tabContainerRef.current) {
      const container = tabContainerRef.current;
      
      // 延迟一点时间确保DOM更新完成
      setTimeout(() => {
        // 滚动到最右侧（最新的Tab位置），额外增加一些距离确保圆角完全可见
        container.scrollTo({
          left: container.scrollWidth + 20,
          behavior: 'smooth'
        });
        setShouldScrollToNew(false);
      }, 50);
    }
  }, [shouldScrollToNew, tabs]);

  // 当激活的Tab改变时，确保它在可视区域内
  useEffect(() => {
    if (activeTabId && tabContainerRef.current) {
      const container = tabContainerRef.current;
      const activeTabElement = container.querySelector(`[data-tab-id="${activeTabId}"]`);
      
      if (activeTabElement) {
        const containerRect = container.getBoundingClientRect();
        const tabRect = activeTabElement.getBoundingClientRect();
        
        // 检查Tab是否完全在可视区域内
        const isVisible = tabRect.left >= containerRect.left && tabRect.right <= containerRect.right;
        
        if (!isVisible) {
          // 滚动到使Tab可见
          activeTabElement.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
          });
        }
      }
    }
  }, [activeTabId]);

  // 处理Tab点击
  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };

  // 处理Tab关闭
  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation(); // 防止触发Tab点击事件
    closeTab(tabId);
  };

  // 直接创建对话Tab
  const handleAddConversationTab = () => {
    createConversationTab();
    setShouldScrollToNew(true);
  };

  // 新增Tab按钮组件
  const AddTabButton = ({ className }: { className?: string }) => (
    <button
      onClick={handleAddConversationTab}
      className={`w-6 h-6 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-150 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 cursor-pointer flex-shrink-0 ${className || ''}`}
      aria-label="添加新对话Tab"
      title="添加新对话Tab"
      type="button"
    >
      <svg
        className="w-4 h-4"
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
    </button>
  );

  return (
    <div className="flex items-center h-full overflow-hidden">
      {/* Tab列表容器 */}
      <div 
        ref={tabContainerRef}
        className="flex items-center space-x-1 overflow-x-auto scrollbar-hide"
        style={{ 
          scrollbarWidth: 'none', 
          msOverflowStyle: 'none',
          scrollBehavior: 'smooth'
        }}
      >
        {tabs.map((tab) => (
          <div 
            key={tab.id}
            onMouseDown={(e) => e.stopPropagation()} // 阻止单个Tab的拖拽
            className="cursor-default"
          >
            <TabItem
              tab={tab}
              isActive={tab.id === activeTabId}
              onClick={() => handleTabClick(tab.id)}
              onClose={(e) => handleTabClose(e, tab.id)}
            />
          </div>
        ))}
        
        {/* 当没有溢出时，新增按钮跟随在Tab后面 */}
        {!isOverflowing && (
          <div 
            className="ml-1"
            onMouseDown={(e) => e.stopPropagation()} // 阻止新增按钮的拖拽
          >
            <AddTabButton />
          </div>
        )}
      </div>

      {/* 当Tab溢出需要滚动时，新增按钮固定在右边 */}
      {isOverflowing && (
        <div 
          className="ml-1 flex-shrink-0"
          onMouseDown={(e) => e.stopPropagation()} // 阻止新增按钮的拖拽
        >
          <AddTabButton />
        </div>
      )}

      {/* 拖拽区域 - 填充剩余空间 */}
      {
        !isOverflowing && (
          <div className="flex-1 min-w-8 cursor-move" title="拖拽移动窗口" />
        )
      }
    </div>
  );
};

/**
 * 单个Tab项组件
 */
interface TabItemProps {
  tab: Tab;
  isActive: boolean;
  onClick: () => void;
  onClose: (e: React.MouseEvent) => void;
}

const TabItem: React.FC<TabItemProps> = ({ tab, isActive, onClick, onClose }) => {
  const getTabIcon = (type: TabType) => {
    switch (type) {
      case TabType.CONVERSATION:
        return (
          <svg
            className="w-4 h-4"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case TabType.CONFIGURATION:
        return (
          <svg
            className="w-4 h-4"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div
      onClick={onClick}
      data-tab-id={tab.id}
      className={`
        flex items-center px-3 py-1.5 rounded-md cursor-pointer transition-all duration-150 select-none w-40 flex-shrink-0
        ${
          isActive
            ? 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
            : 'bg-transparent text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'
        }
      `}
    >
      {/* Tab图标 */}
      <div className="flex-shrink-0 mr-2 text-current">
        {getTabIcon(tab.type)}
      </div>
      
      {/* Tab标题 */}
      <span className="flex-1 min-w-0 text-sm font-medium truncate">
        {tab.title}
      </span>
      
      {/* 关闭按钮 */}
      {tab.canClose && (
        <button
          onClick={onClose}
          className="flex-shrink-0 ml-2 w-4 h-4 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-150 flex items-center justify-center opacity-60 hover:opacity-100 focus:opacity-100"
          aria-label={`关闭 ${tab.title}`}
          title="关闭Tab"
        >
          <svg
            className="w-3 h-3"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default TabBar; 