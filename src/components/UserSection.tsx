'use client';

import React, { useState } from 'react';
import useAppStore from '@/stores/useAppStore';
import useTabStore from '@/stores/useTabStore';

/**
 * 用户区域组件
 * 显示用户头像和配置入口，处理登录状态
 */
const UserSection: React.FC = () => {
  const { user, isAuthenticated, toggleDarkMode, isDarkMode } = useAppStore();
  const { createConfigurationTab } = useTabStore();
  
  const [showUserMenu, setShowUserMenu] = useState(false);

  // 打开配置Tab
  const handleOpenConfiguration = () => {
    createConfigurationTab();
    setShowUserMenu(false);
  };

  // 处理登录按钮点击
  const handleLogin = () => {
    // TODO: 实现登录逻辑
    console.log('触发登录');
    setShowUserMenu(false);
  };

  // 处理登出
  const handleLogout = () => {
    // TODO: 实现登出逻辑
    console.log('触发登出');
    setShowUserMenu(false);
  };

  return (
    <div className="flex items-center space-x-3">
      {/* 深色模式切换按钮 */}
      <button
        onClick={toggleDarkMode}
        className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-150 flex items-center justify-center text-gray-600 dark:text-gray-300"
        aria-label={isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
        title={isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
      >
        {isDarkMode ? (
          // 太阳图标 (浅色模式)
          <svg
            className="w-4 h-4"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        ) : (
          // 月亮图标 (深色模式)
          <svg
            className="w-4 h-4"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        )}
      </button>

      {/* 配置按钮 */}
      <button
        onClick={handleOpenConfiguration}
        className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-150 flex items-center justify-center text-gray-600 dark:text-gray-300"
        aria-label="打开配置"
        title="配置"
      >
        <svg
          className="w-4 h-4"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </button>

      {/* 用户头像区域 */}
      <div className="relative">
        <button
          onClick={() => setShowUserMenu(!showUserMenu)}
          className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600 hover:ring-2 hover:ring-blue-500 transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="用户菜单"
        >
          {isAuthenticated && user?.avatar ? (
            <img
              src={user.avatar}
              alt={user.name || '用户头像'}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
              <svg
                className="w-5 h-5"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
          )}
        </button>

        {/* 用户菜单下拉 */}
        {showUserMenu && (
          <div className="absolute top-full right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg z-50">
            {isAuthenticated ? (
              <>
                {/* 已登录用户信息 */}
                <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {user?.name || '用户'}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {user?.email || ''}
                  </div>
                </div>
                
                <div className="py-1">
                  <button
                    onClick={handleOpenConfiguration}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                  >
                    配置设置
                  </button>
                  <button
                    onClick={handleLogout}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                  >
                    退出登录
                  </button>
                </div>
              </>
            ) : (
              <>
                {/* 未登录状态 */}
                <div className="py-1">
                  <button
                    onClick={handleLogin}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                  >
                    登录
                  </button>
                  <button
                    onClick={handleOpenConfiguration}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                  >
                    配置设置
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserSection; 