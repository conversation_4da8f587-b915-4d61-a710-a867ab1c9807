'use client';

import React, { useState } from 'react';
import {
  Cog6ToothIcon,
  ComputerDesktopIcon,
  CloudIcon,
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  BookOpenIcon,
  CircleStackIcon,
  CommandLineIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import useConfigStore from '@/stores/useConfigStore';

interface ConfigurationTabContentProps {
    tab: any;
}

// 配置菜单项类型
interface ConfigMenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
}

// 配置菜单项
const configMenuItems: ConfigMenuItem[] = [
  { id: 'general', label: '通用设置', icon: Cog6ToothIcon },
  { id: 'display', label: '显示设置', icon: ComputerDesktopIcon },
  { id: 'providers', label: '服务商设置', icon: CloudIcon },
  { id: 'mcp', label: 'MCP设置', icon: CpuChipIcon },
  { id: 'prompts', label: 'Prompt管理', icon: ChatBubbleLeftRightIcon },
  { id: 'knowledge', label: '知识库', icon: BookOpenIcon },
  { id: 'data', label: '数据设置', icon: CircleStackIcon },
  { id: 'shortcuts', label: '快捷键', icon: CommandLineIcon },
  { id: 'about', label: '关于', icon: InformationCircleIcon },
];

// 获取服务商图标
const getProviderIcon = (providerId: string): string => {
  const icons: Record<string, string> = {
    ollama: 'O',
    deepseek: 'D',
    qiniu: '七',
    siliconflow: 'S',
    doubao: '豆',
    minimax: 'M',
    fireworks: 'F',
    paimeng: 'P',
    'openai-responses': 'OR',
    openai: 'AI',
    gemini: 'G',
    'github-models': 'GH',
  };
  return icons[providerId] || providerId.charAt(0).toUpperCase();
};

// 获取服务商图标样式
const getProviderIconStyle = (providerId: string): string => {
  const styles: Record<string, string> = {
    ollama: 'bg-gray-600',
    deepseek: 'bg-blue-600',
    qiniu: 'bg-green-600',
    siliconflow: 'bg-purple-600',
    doubao: 'bg-orange-600',
    minimax: 'bg-red-600',
    fireworks: 'bg-yellow-600',
    paimeng: 'bg-pink-600',
    'openai-responses': 'bg-teal-600',
    openai: 'bg-green-700',
    gemini: 'bg-blue-700',
    'github-models': 'bg-gray-800',
  };
  return styles[providerId] || 'bg-gray-500';
};

const ConfigurationTabContent: React.FC<ConfigurationTabContentProps> = ({ tab }) => {
  const [activeMenuItem, setActiveMenuItem] = useState('providers');

  const renderConfigContent = () => {
    switch (activeMenuItem) {
      case 'providers':
        return <ProviderSettings />;
      case 'general':
        return <div className="p-6">通用设置内容</div>;
      case 'display':
        return <div className="p-6">显示设置内容</div>;
      case 'mcp':
        return <div className="p-6">MCP设置内容</div>;
      case 'prompts':
        return <div className="p-6">Prompt管理内容</div>;
      case 'knowledge':
        return <div className="p-6">知识库内容</div>;
      case 'data':
        return <div className="p-6">数据设置内容</div>;
      case 'shortcuts':
        return <div className="p-6">快捷键内容</div>;
      case 'about':
        return <div className="p-6">关于内容</div>;
      default:
        return <div className="p-6">请选择配置项</div>;
    }
  };

  return (
    <div className="flex h-full bg-white dark:bg-gray-900">
      {/* 左侧配置菜单 */}
      <div className="w-64 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div className="p-4">
          <nav className="space-y-1">
            {configMenuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeMenuItem === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => setActiveMenuItem(item.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* 右侧配置内容 */}
      <div className="flex-1 overflow-auto">
        {renderConfigContent()}
      </div>
    </div>
  );
};

// 服务商设置组件
const ProviderSettings: React.FC = () => {
  return (
    <div className="flex h-full">
      <ProviderList />
      <ProviderDetail />
    </div>
  );
};

// 服务商列表组件
const ProviderList: React.FC = () => {
  const { providers, selectedProviderId, setSelectedProvider, toggleProvider } = useConfigStore();

  return (
    <div className="w-80 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 flex-shrink-0">
      <div className="p-4">
        <div className="space-y-1">
          {providers.map((provider) => (
            <div
              key={provider.id}
              className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                selectedProviderId === provider.id
                  ? 'bg-blue-50 dark:bg-blue-900/20'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-800'
              }`}
              onClick={() => setSelectedProvider(provider.id)}
            >
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {/* 服务商图标 */}
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getProviderIconStyle(provider.id)}`}>
                    <span className="text-xs font-medium text-white">
                      {getProviderIcon(provider.id)}
                    </span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {provider.name}
                  </p>
                </div>
              </div>

              {/* 开关按钮 */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleProvider(provider.id);
                }}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  provider.enabled
                    ? 'bg-blue-600'
                    : 'bg-gray-200 dark:bg-gray-700'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    provider.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// 服务商详情组件
const ProviderDetail: React.FC = () => {
  const {
    providers,
    selectedProviderId,
    updateProviderApiKey,
    updateProviderApiUrl,
    validateApiKey,
    toggleProviderModel,
    enableAllModels,
    disableAllModels
  } = useConfigStore();

  const [isValidating, setIsValidating] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);

  const selectedProvider = providers.find(p => p.id === selectedProviderId);

  if (!selectedProvider) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">请选择一个服务商</p>
      </div>
    );
  }

  const handleValidateApiKey = async () => {
    if (!selectedProvider.apiKey.trim()) {
      alert('请先输入API Key');
      return;
    }

    setIsValidating(true);
    try {
      const isValid = await validateApiKey(selectedProvider.id);
      if (isValid) {
        alert('API Key验证成功！');
      } else {
        alert('API Key验证失败，请检查密钥是否正确');
      }
    } catch (error) {
      alert('验证过程中出现错误');
    } finally {
      setIsValidating(false);
    }
  };

  const handleGetApiKey = () => {
    // 根据不同服务商打开对应的获取API Key页面
    const urls: Record<string, string> = {
      openai: 'https://platform.openai.com/api-keys',
      deepseek: 'https://platform.deepseek.com/api-keys',
      gemini: 'https://makersuite.google.com/app/apikey',
      // 可以添加更多服务商的URL
    };

    const url = urls[selectedProvider.id];
    if (url) {
      window.open(url, '_blank');
    } else {
      alert('请访问该服务商官网获取API Key');
    }
  };

  return (
    <div className="flex-1 bg-white dark:bg-gray-900 overflow-auto">
      <div className="p-6">
        <div className="max-w-2xl">
          {/* 服务商标题 */}
          <div className="flex items-center space-x-3 mb-6">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getProviderIconStyle(selectedProvider.id)}`}>
              <span className="text-sm font-medium text-white">
                {getProviderIcon(selectedProvider.id)}
              </span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {selectedProvider.name}
            </h2>
          </div>

          {/* API URL配置 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API URL
            </label>
            <input
              type="text"
              value={selectedProvider.apiUrl}
              onChange={(e) => updateProviderApiUrl(selectedProvider.id, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              placeholder="请输入API URL"
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              API例: {selectedProvider.defaultApiUrl || 'https://api.openai.com/v1'}
            </p>
          </div>

          {/* API Key配置 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API Key
            </label>
            <div className="relative">
              <input
                type={showApiKey ? "text" : "password"}
                value={selectedProvider.apiKey}
                onChange={(e) => updateProviderApiKey(selectedProvider.id, e.target.value)}
                className="w-full px-3 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                placeholder="请输入API Key"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
              >
                {showApiKey ? '隐藏' : '显示'}
              </button>
            </div>

            {/* API Key操作按钮 */}
            <div className="mt-3 flex space-x-3">
              <button
                onClick={handleValidateApiKey}
                disabled={isValidating || !selectedProvider.apiKey.trim()}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isValidating ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    验证中...
                  </>
                ) : (
                  '验证密钥'
                )}
              </button>

              <button
                onClick={handleGetApiKey}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                如何获取
              </button>
            </div>

            {/* 验证状态显示 */}
            {selectedProvider.keyValidated !== undefined && (
              <div className={`mt-2 flex items-center text-sm ${
                selectedProvider.keyValidated
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              }`}>
                <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                  selectedProvider.keyValidated ? 'bg-green-500' : 'bg-red-500'
                }`}></span>
                {selectedProvider.keyValidated ? 'API Key已验证' : 'API Key验证失败'}
              </div>
            )}
          </div>

          {/* 模型列表 */}
          <ModelList provider={selectedProvider} />
        </div>
      </div>
    </div>
  );
};

// 模型列表组件
interface ModelListProps {
  provider: any;
}

const ModelList: React.FC<ModelListProps> = ({ provider }) => {
  const { toggleProviderModel, enableAllModels, disableAllModels } = useConfigStore();

  if (!provider.models || provider.models.length === 0) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">模型列表</h3>
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>暂无可用模型</p>
          <p className="text-sm mt-1">请确保API Key配置正确后重新加载</p>
        </div>
      </div>
    );
  }

  const enabledModelsCount = provider.models.filter((model: any) => model.enabled).length;
  const totalModelsCount = provider.models.length;

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">模型列表</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {enabledModelsCount}/{totalModelsCount} 模型已启用
          </span>
        </div>
      </div>

      {/* 模型操作按钮 */}
      <div className="flex items-center space-x-2 mb-4">
        <button
          onClick={() => enableAllModels(provider.id)}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800"
        >
          启用全部
        </button>
        <button
          onClick={() => disableAllModels(provider.id)}
          className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          禁用全部
        </button>
      </div>

      {/* 模型列表 */}
      <div className="space-y-2">
        {provider.models.map((model: any) => (
          <div
            key={model.id}
            className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {model.name}
                </p>
                <div className="flex items-center space-x-4 mt-1">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                    default
                  </span>
                  {model.enabled && (
                    <span className="inline-flex items-center text-xs text-green-600 dark:text-green-400">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></span>
                      已启用
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* 模型开关 */}
            <button
              onClick={() => toggleProviderModel(provider.id, model.id)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                model.enabled
                  ? 'bg-blue-600'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  model.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ConfigurationTabContent;