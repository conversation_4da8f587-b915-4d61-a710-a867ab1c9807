'use client';

import React, { useEffect } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import WindowControls from './WindowControls';
import TabBar from './TabBar';
import UserSection from './UserSection';
import useAppStore from '@/stores/useAppStore';

/**
 * 自定义标题栏组件
 * iOS风格的导航栏，包含窗口控制、Tab管理和用户区域
 */
const CustomTitleBar: React.FC = () => {
  const { isDarkMode } = useAppStore();

  // 初始化时应用主题
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  // 处理拖拽区域的双击事件（最大化/还原窗口）
  const handleDragRegionDoubleClick = async () => {
    try {
      const appWindow = getCurrentWindow();
      const isMaximized = await appWindow.isMaximized();
      if (isMaximized) {
        await appWindow.unmaximize();
      } else {
        await appWindow.maximize();
      }
    } catch (error) {
      console.error('Failed to toggle maximize:', error);
    }
  };

  // 处理拖拽开始
  const handleDragStart = async (e: React.MouseEvent) => {
    // 只有左键单击且不是双击时才开始拖拽
    if (e.button === 0 && e.detail === 1) {
      try {
        const appWindow = getCurrentWindow();
        await appWindow.startDragging();
      } catch (error) {
        console.error('Failed to start dragging:', error);
      }
    }
  };

  return (
    <div 
      className="h-12 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 select-none cursor-move"
      onMouseDown={handleDragStart}
      onDoubleClick={handleDragRegionDoubleClick}
      data-tauri-drag-region
    >
      {/* 左侧：窗口控制按钮 */}
      <div 
        className="flex items-center space-x-4 cursor-default"
        onMouseDown={(e) => e.stopPropagation()} // 阻止窗口控制按钮区域的拖拽
      >
        <WindowControls />
      </div>

      {/* 中间：Tab栏 */}
      <div className="flex-1 flex mx-2 min-w-0">
        <div className="w-full h-8">
          <TabBar />
        </div>
      </div>

      {/* 右侧：用户区域 */}
      <div 
        className="flex items-center cursor-default"
        onMouseDown={(e) => e.stopPropagation()} // 阻止用户区域的拖拽
      >
        <UserSection />
      </div>
    </div>
  );
};

export default CustomTitleBar; 