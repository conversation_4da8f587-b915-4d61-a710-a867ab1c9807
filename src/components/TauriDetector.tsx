'use client';

import { useEffect } from 'react';

/**
 * Tauri 环境检测组件
 * 用于在客户端检测是否运行在 Tauri 桌面应用中，并应用相应的样式
 */
const TauriDetector: React.FC = () => {
  useEffect(() => {
    // 检测是否在 Tauri 环境中
    const isTauri = typeof window !== 'undefined' && (window as any).__TAURI__;
    
    if (isTauri) {
      document.documentElement.setAttribute('data-tauri', 'true');
      console.log('Running in Tauri environment');
    } else {
      document.documentElement.removeAttribute('data-tauri');
      console.log('Running in web browser');
    }
  }, []);

  return null; // 这个组件不渲染任何内容
};

export default TauriDetector;
