'use client';

import React, { useEffect, useRef } from 'react';
import { Tab } from '@/types';
import useConversationStore from '@/stores/useConversationStore';
import { useTabLifecycle, useTabErrorBoundary } from '@/hooks/useTabLifecycle';
import { 
  PlusIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon, 
  PencilIcon,
  PaperClipIcon,
  GlobeAltIcon,
  ArrowUpCircleIcon,
  EllipsisHorizontalIcon,
  Cog6ToothIcon,
  TrashIcon,

  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';
import './styles.css'; // 导入样式文件

interface ConversationTabContentProps {
  tab: Tab;
}

/**
 * 对话Tab内容组件
 * 展示对话界面，包括左侧对话列表和右侧对话内容
 * 每个tab都有独立的状态，不会相互影响
 */
const ConversationTabContent: React.FC<ConversationTabContentProps> = ({ tab }) => {
  // 所有hooks必须在任何条件渲染之前调用，确保调用顺序一致
  
  // 1. 自定义hooks
  const { tabState } = useTabLifecycle(tab.id);
  const { error: tabError, clearError, setTabError } = useTabErrorBoundary(tab.id);
  
  // 2. Store hooks
  const {
    setSidebarCollapsed,
    setHoveredItem,
    setShowMenu,
    setShowToolMenu,
    setShowSettingsMenu,
    setSystemPrompt,
    updateMCPTool,
    setActiveConversation,
    deleteConversation,
    updateConversation,
    addMessage,
    setInputContent,
    setLoading,
  } = useConversationStore();
  
  // 3. Refs
  const menuRef = useRef<HTMLDivElement>(null);
  const settingsMenuRef = useRef<HTMLDivElement>(null);
  const toolMenuRef = useRef<HTMLDivElement>(null);
  const menuButtonRef = useRef<Record<string, HTMLButtonElement | null>>({});
  const settingsButtonRef = useRef<HTMLButtonElement>(null);
  const toolButtonRef = useRef<HTMLButtonElement>(null);

  // 4. 从tabState安全解构，提供默认值
  const {
    sidebarCollapsed = false,
    hoveredItem = null,
    showMenu = null,
    showToolMenu = false,
    showSettingsMenu = false,
    systemPrompt = '',
    selectedModel = 'Qwen/QwQ-32B-Preview',
    mcpTools = [],
    conversations = [],
    messages = [],
    inputContent = '',
    loading = false,
  } = tabState || {};

  // 5. useEffect hooks - 确保依赖项完整
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 处理对话列表菜单
      if (showMenu && menuRef.current && !menuRef.current.contains(event.target as Node)) {
        const buttonEl = menuButtonRef.current[showMenu];
        if (!buttonEl || !buttonEl.contains(event.target as Node)) {
          setShowMenu(tab.id, null);
        }
      }
      
      // 处理设置菜单
      if (showSettingsMenu && settingsMenuRef.current && 
          !settingsMenuRef.current.contains(event.target as Node) && 
          settingsButtonRef.current && 
          !settingsButtonRef.current.contains(event.target as Node)) {
        setShowSettingsMenu(tab.id, false);
      }
      
      // 处理工具菜单
      if (showToolMenu && toolMenuRef.current && 
          !toolMenuRef.current.contains(event.target as Node) && 
          toolButtonRef.current && 
          !toolButtonRef.current.contains(event.target as Node)) {
        setShowToolMenu(tab.id, false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu, showSettingsMenu, showToolMenu, tab.id, setShowMenu, setShowSettingsMenu, setShowToolMenu]);

  // 6. 早期返回 - 在所有hooks之后
  if (!tabState) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-gray-500">初始化中...</div>
      </div>
    );
  }

  // 处理对话列表项菜单
  const handleMenuClick = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(tab.id, showMenu === id ? null : id);
  };
  
  // 设置菜单按钮引用
  const setMenuButtonRef = (id: string) => (el: HTMLButtonElement | null) => {
    menuButtonRef.current[id] = el;
  };

  // 处理对话重命名
  const handleRenameConversation = (conversationId: string) => {
    const newTitle = prompt('请输入新的对话标题');
    if (newTitle && newTitle.trim()) {
      updateConversation(tab.id, conversationId, { title: newTitle.trim() });
    }
    setShowMenu(tab.id, null);
  };

  // 处理对话删除
  const handleDeleteConversation = (conversationId: string) => {
    if (confirm('确定要删除这个对话吗？')) {
      deleteConversation(tab.id, conversationId);
    }
    setShowMenu(tab.id, null);
  };

  // 处理MCP工具切换
  const handleToggleMCPTool = (toolId: string) => {
    const tool = mcpTools.find(t => t.id === toolId);
    if (tool) {
      updateMCPTool(tab.id, toolId, { enabled: !tool.enabled });
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputContent.trim()) return;
    
    const userMessage = inputContent;
    
    try {
      // 清除之前的错误
      clearError();
      
      // 添加用户消息
      addMessage(tab.id, {
        content: userMessage,
        sender: 'user',
        status: 'sent',
      });
      
      // 清空输入框
      setInputContent(tab.id, '');
      
      // 开始加载
      setLoading(tab.id, true);
      
      // 模拟AI回复（实际项目中这里应该调用API）
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // 随机模拟成功或失败
      if (Math.random() > 0.8) {
        throw new Error('网络连接超时，请重试');
      }
      
      addMessage(tab.id, {
        content: `这是对"${userMessage}"的AI回复。在真实应用中，这里会调用AI模型API来生成回复。每个tab都有独立的对话状态，互不影响。`,
        sender: 'assistant',
        status: 'delivered',
      });
      
    } catch (error) {
      console.error('发送消息失败:', error);
      setTabError(error instanceof Error ? error.message : '发送消息失败，请重试');
    } finally {
      setLoading(tab.id, false);
    }
  };

  // 处理输入框按键事件
  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex h-full">
      {/* 左侧对话列表 */}
      <div className={`bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all flex flex-col ${sidebarCollapsed ? 'w-0 overflow-hidden' : 'w-64'}`}>
        {/* 新会话按钮 - 固定在顶部，移除底部边框 */}
        <div className="px-2 py-2">
          <button className="w-full flex items-center justify-center gap-2 py-1.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm cursor-pointer">
            <PlusIcon className="w-4 h-4" />
            <span>新会话</span>
          </button>
        </div>
        
        {/* 滚动区域 */}
        <div className="flex-1 overflow-y-auto">
          {/* 日期显示 */}
          <div className="px-4 text-gray-500 text-sm sticky top-0 bg-gray-50 dark:bg-gray-800 z-10">
            2025-05-24
          </div>
          
          {conversations.map((conversation) => (
            <div 
              key={conversation.id}
              className={`relative px-2 py-1 cursor-pointer text-sm group`}
              onMouseEnter={() => setHoveredItem(tab.id, conversation.id)}
              onMouseLeave={() => setHoveredItem(tab.id, null)}
              onClick={() => setActiveConversation(tab.id, conversation.id)}
            >
              <div className={`flex items-center px-2 py-1 ${conversation.active ? 'bg-gray-200 dark:bg-gray-700 rounded-lg' : ''}`}>
                <div className="truncate flex-1">{conversation.title}</div>
                {(hoveredItem === conversation.id || showMenu === conversation.id) && (
                  <button 
                    className="rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 flex-shrink-0 ml-1 cursor-pointer"
                    onClick={(e) => handleMenuClick(conversation.id, e)}
                    ref={setMenuButtonRef(conversation.id)}
                  >
                    <EllipsisHorizontalIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  </button>
                )}
              </div>
              
              {/* 下拉菜单 */}
              {showMenu === conversation.id && (
                <div 
                  ref={menuRef}
                  className="absolute right-2 mt-1 w-32 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 shadow-lg rounded-lg z-10 py-1 animate-fade-in"
                >
                  <button 
                    className="w-full text-left px-3 py-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2 cursor-pointer"
                    onClick={() => handleRenameConversation(conversation.id)}
                  >
                    <PencilIcon className="w-4 h-4" />
                    <span>重命名</span>
                  </button>
                  <button 
                    className="w-full text-left px-3 py-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 text-red-500 flex items-center gap-2 cursor-pointer"
                    onClick={() => handleDeleteConversation(conversation.id)}
                  >
                    <TrashIcon className="w-4 h-4" />
                    <span>删除</span>
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 右侧对话内容 */}
      <div className="flex-1 flex flex-col h-full bg-white dark:bg-gray-900">
        {/* 顶部模型选择 */}
        <div className="flex items-center p-2">
          <button 
            onClick={() => setSidebarCollapsed(tab.id, !sidebarCollapsed)}
            className="p-1 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
          >
            {sidebarCollapsed ? <ChevronRightIcon className="w-5 h-5" /> : <ChevronLeftIcon className="w-5 h-5" />}
          </button>
          
          <div className="flex items-center ml-4 gap-2 px-3 py-1 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
            <svg className="w-5 h-5 text-indigo-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 4L4 8L12 12L20 8L12 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M4 16L12 20L20 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M4 12L12 16L20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span>{selectedModel}</span>
            <ChevronRightIcon className="w-4 h-4" />
          </div>
          
          <div className="flex-1"></div>
          
          <div className="relative">
            <button 
              ref={settingsButtonRef}
              className="p-1 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
              onClick={() => setShowSettingsMenu(tab.id, !showSettingsMenu)}
            >
              <Cog6ToothIcon className="w-5 h-5" />
            </button>
            
            {/* 设置下拉菜单 */}
            {showSettingsMenu && (
              <div 
                ref={settingsMenuRef}
                className="absolute right-0 mt-1 w-72 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 shadow-lg rounded-lg z-10 animate-fade-in"
              >
                <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium">模型设置</div>
                  </div>
                  
                  <div className="mt-4">
                    <div className="flex items-center gap-1 mb-2 text-sm text-gray-600">
                      <span>系统提示词</span>
                      <QuestionMarkCircleIcon 
                        className="w-4 h-4 cursor-pointer" 
                        title="设置AI助手的系统提示词，用于定义其行为和角色"
                      />
                    </div>
                    <div className="relative bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600">
                      <textarea 
                        className="w-full h-32 p-3 bg-transparent rounded-lg resize-none focus:outline-none text-sm"
                        placeholder="请输入系统提示词..."
                        value={systemPrompt}
                        onChange={(e) => setSystemPrompt(tab.id, e.target.value)}
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 错误提示 */}
        {tabError && (
          <div className="mx-2 mb-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            <div className="flex items-center justify-between">
              <span>{tabError}</span>
              <button 
                onClick={clearError}
                className="ml-2 text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* 对话内容区 */}
        <div className="flex-1 overflow-y-auto p-2">
          <div className="max-w-6xl mx-auto">
            {messages.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <div className="text-lg mb-2">开始新对话</div>
                  <div className="text-sm">向我提问任何问题吧！</div>
                </div>
              </div>
            ) : (
              messages.map((message) => (
                <div key={message.id} className="mb-6">
                  {message.sender === 'assistant' ? (
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8">
                        <svg className="w-full h-full text-indigo-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 4L4 8L12 12L20 8L12 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M4 16L12 20L20 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M4 12L12 16L20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">
                            {selectedModel}
                          </span>
                          <span className="text-xs text-gray-500">
                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                        </div>
                        <div className="text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                          {message.content}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex justify-end mb-4">
                      <div className="flex items-start gap-2">
                        <div className="bg-blue-50 dark:bg-blue-900 rounded-lg px-4 py-2 text-gray-800 dark:text-gray-200 max-w-md">
                          {message.content}
                        </div>
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500">
                          U
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
            
            {/* 加载状态 */}
            {loading && (
              <div className="flex items-center gap-3 mb-6">
                <div className="flex-shrink-0 w-8 h-8">
                  <svg className="w-full h-full text-indigo-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4L4 8L12 12L20 8L12 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M4 16L12 20L20 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M4 12L12 16L20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{selectedModel}</span>
                  </div>
                  <div className="text-gray-500">正在思考中...</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 底部输入区 */}
        <div className="p-2">
          <div className="max-w-6xl mx-auto">
            <div className="relative rounded-lg border border-gray-300 dark:border-gray-600">
              <textarea
                className="w-full min-h-16 max-h-40 p-3 resize-none border-none outline-none bg-transparent"
                placeholder="问点什么？可以通过@来引用工具、文件、资源..."
                value={inputContent}
                onChange={(e) => setInputContent(tab.id, e.target.value)}
                onKeyDown={handleInputKeyDown}
              />
              
              <div className="flex items-center px-3 py-2">
                <div className="flex gap-2">
                  <button className="p-1 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                    <PaperClipIcon className="w-5 h-5 text-gray-500" />
                  </button>
                  <button className="p-1 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                    <GlobeAltIcon className="w-5 h-5 text-gray-500" />
                  </button>
                  <div className="relative">
                    <button 
                      ref={toolButtonRef}
                      className={`flex items-center justify-center p-1 rounded-lg cursor-pointer ${mcpTools.filter(tool => tool.enabled).length > 0 ? 'bg-blue-500 text-white' : ''}`}
                      onClick={() => setShowToolMenu(tab.id, !showToolMenu)}
                    >
                      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span className="text-xs ml-1">{mcpTools.filter(tool => tool.enabled).length}</span>
                    </button>
                    
                    {/* MCP工具菜单 */}
                    {showToolMenu && (
                      <div 
                        ref={toolMenuRef}
                        className="absolute bottom-full left-0 mb-2 w-72 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 shadow-lg rounded-lg z-10 py-1 animate-fade-in"
                      >
                        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">启用MCP</div>
                            <div 
                              className={`relative inline-block w-10 h-6 transition duration-200 ease-in-out rounded-full cursor-pointer ${mcpTools.find(t => t.id === 'mcp-enable')?.enabled ? 'bg-blue-500' : 'bg-gray-300'}`}
                              onClick={() => handleToggleMCPTool('mcp-enable')}
                            >
                              <label className={`absolute left-0 top-0 w-6 h-6 bg-white rounded-full transition-transform duration-200 ease-in-out transform ${mcpTools.find(t => t.id === 'mcp-enable')?.enabled ? 'translate-x-full' : ''} shadow-md cursor-pointer`}></label>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500">启用MCP功能以使用工具调用</div>
                        </div>
                        
                        {mcpTools.slice(1).map((tool) => (
                          <div key={tool.id} className="px-3 py-2 flex items-center justify-between hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                            <div className="flex items-center gap-2">
                              {tool.icon && <span>{tool.icon}</span>}
                              <span>{tool.name}</span>
                            </div>
                            <div 
                              className={`relative inline-block w-10 h-6 transition duration-200 ease-in-out rounded-full cursor-pointer ${tool.enabled ? 'bg-blue-500' : 'bg-gray-300'}`}
                              onClick={() => handleToggleMCPTool(tool.id)}
                            >
                              <label className={`absolute left-0 top-0 w-6 h-6 bg-white rounded-full transition-transform duration-200 ease-in-out transform ${tool.enabled ? 'translate-x-full' : ''} shadow-md cursor-pointer`}></label>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex-1" />
                
                <button 
                  className="p-1 rounded-full text-blue-500 hover:bg-blue-50 dark:hover:bg-gray-800 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={handleSendMessage}
                  disabled={!inputContent.trim() || loading}
                >
                  <ArrowUpCircleIcon className="w-6 h-6" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversationTabContent;