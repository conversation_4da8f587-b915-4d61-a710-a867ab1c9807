'use client';

import React, { useEffect } from 'react';
import useTabStore from '@/stores/useTabStore';
import useConversationStore from '@/stores/useConversationStore';
import { TabType } from '@/types';
import ConversationTabContent from './ConversationTabContent';
import ConfigurationTabContent from './ConfigurationTabContent';

/**
 * Tab内容组件
 * 根据当前激活的Tab类型显示相应内容
 * 确保每个tab都有独立的状态管理
 */
const TabContent: React.FC = () => {
  const { getActiveTab, tabs } = useTabStore();
  const { removeTab: removeConversationTab } = useConversationStore();
  const activeTab = getActiveTab();

  // 监听tabs变化，清理已删除的对话tab状态
  useEffect(() => {
    const conversationStore = useConversationStore.getState();
    const currentTabIds = new Set(tabs.map(tab => tab.id));
    const storedTabIds = Array.from(conversationStore.tabStates.keys());
    
    // 清理不存在的tab状态
    storedTabIds.forEach(tabId => {
      if (!currentTabIds.has(tabId)) {
        removeConversationTab(tabId);
      }
    });
  }, [tabs, removeConversationTab]);

  if (!activeTab) {
    return (
      <div className="flex h-full items-center justify-center text-gray-500">
        <div className="text-center">
          <div className="text-lg mb-2">欢迎使用MCP客户端</div>
          <div className="text-sm">请创建一个新的对话或配置tab开始使用</div>
        </div>
      </div>
    );
  }

  switch (activeTab.type) {
    case TabType.CONVERSATION:
      return <ConversationTabContent tab={activeTab} />;
    case TabType.CONFIGURATION:
      return <ConfigurationTabContent tab={activeTab} />;
    default:
      return (
        <div className="flex h-full items-center justify-center text-gray-500">
          <div>未知的Tab类型: {activeTab.type}</div>
        </div>
      );
  }
};

export default TabContent; 