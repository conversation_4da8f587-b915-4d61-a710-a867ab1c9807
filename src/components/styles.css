/* 添加动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out forwards;
}

/* 移除contentEditable元素的焦点边框 */
[contenteditable]:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 移除textarea的焦点边框 */
textarea:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
} 