'use client';

import React from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';

/**
 * iOS风格的窗口控制按钮组件
 * 提供关闭、最小化、最大化功能
 */
const WindowControls: React.FC = () => {
  
  // 关闭窗口
  const handleClose = async () => {
    try {
      const appWindow = getCurrentWindow();
      await appWindow.close();
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  };

  // 最小化窗口
  const handleMinimize = async () => {
    try {
      const appWindow = getCurrentWindow();
      await appWindow.minimize();
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  };

  // 最大化/还原窗口
  const handleMaximize = async () => {
    try {
      const appWindow = getCurrentWindow();
      const isMaximized = await appWindow.isMaximized();
      if (isMaximized) {
        await appWindow.unmaximize();
      } else {
        await appWindow.maximize();
      }
    } catch (error) {
      console.error('Failed to toggle maximize:', error);
    }
  };

  return (
    <div className="flex items-center space-x-2 select-none">
      {/* 关闭按钮 - 红色 */}
      <button
        onClick={handleClose}
        className="w-3 h-3 rounded-full bg-red-500 hover:bg-red-600 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-300 focus:ring-opacity-50"
        aria-label="关闭窗口"
        title="关闭"
      >
        <span className="sr-only">关闭</span>
      </button>
      
      {/* 最小化按钮 - 黄色 */}
      <button
        onClick={handleMinimize}
        className="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-600 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-yellow-300 focus:ring-opacity-50"
        aria-label="最小化窗口"
        title="最小化"
      >
        <span className="sr-only">最小化</span>
      </button>
      
      {/* 最大化按钮 - 绿色 */}
      <button
        onClick={handleMaximize}
        className="w-3 h-3 rounded-full bg-green-500 hover:bg-green-600 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-green-300 focus:ring-opacity-50"
        aria-label="最大化/还原窗口"
        title="最大化"
      >
        <span className="sr-only">最大化</span>
      </button>
    </div>
  );
};

export default WindowControls; 