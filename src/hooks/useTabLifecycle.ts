import { useEffect } from 'react';
import useConversationStore from '@/stores/useConversationStore';

/**
 * Tab生命周期管理hook
 * 负责tab的初始化和清理工作
 */
export const useTabLifecycle = (tabId: string) => {
  const { initializeTab, removeTab, getTabState } = useConversationStore();

  // 初始化tab状态
  useEffect(() => {
    initializeTab(tabId);
  }, [tabId, initializeTab]);

  // 组件卸载时不立即清理，由TabContent统一管理
  // 这样可以避免tab切换时状态丢失

  return {
    tabState: getTabState(tabId),
    isInitialized: !!getTabState(tabId),
  };
};

/**
 * 获取tab的错误边界信息
 */
export const useTabErrorBoundary = (tabId: string) => {
  const { getTabState, setError } = useConversationStore();
  const tabState = getTabState(tabId);

  const clearError = () => {
    setError(tabId, undefined);
  };

  const setTabError = (error: string) => {
    setError(tabId, error);
  };

  return {
    error: tabState?.error,
    clearError,
    setTabError,
  };
}; 