'use client';

import React from 'react';
import useTabStore from '@/stores/useTabStore';
import { TabType } from '@/types';

export default function TestPage() {
  const {
    tabs,
    activeTabId,
    createConversationTab,
    createConfigurationTab,
    setActiveTab,
    closeTab,
  } = useTabStore();

  const handleCreateConversation = () => {
    console.log('测试页面：创建对话Tab');
    const tabId = createConversationTab();
    console.log('创建的对话Tab ID:', tabId);
    alert(`创建了对话Tab: ${tabId}`);
  };

  const handleCreateConfiguration = () => {
    console.log('测试页面：创建配置Tab');
    const tabId = createConfigurationTab();
    console.log('创建的配置Tab ID:', tabId);
    alert(`创建了配置Tab: ${tabId}`);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Tab功能测试页面</h1>
      
      {/* 创建Tab按钮 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">创建Tab测试</h2>
        <div className="flex gap-4">
          <button
            onClick={handleCreateConversation}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-150"
          >
            创建对话Tab
          </button>
          <button
            onClick={handleCreateConfiguration}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-150"
          >
            创建配置Tab
          </button>
        </div>
      </div>

      {/* Tab列表显示 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">当前Tab列表</h2>
        <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            共有 {tabs.length} 个Tab，激活的Tab ID: {activeTabId || '无'}
          </p>
          
          {tabs.length === 0 ? (
            <p className="text-gray-500 italic">暂无Tab</p>
          ) : (
            <div className="space-y-2">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className={`flex items-center justify-between p-3 rounded-md border ${
                    tab.id === activeTabId
                      ? 'bg-blue-100 dark:bg-blue-900 border-blue-300 dark:border-blue-700'
                      : 'bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-mono text-gray-500">
                      {tab.id.slice(-8)}
                    </span>
                    <span className="font-medium">
                      {tab.title}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      tab.type === TabType.CONVERSATION
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }`}>
                      {tab.type === TabType.CONVERSATION ? '对话' : '配置'}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {tab.id !== activeTabId && (
                      <button
                        onClick={() => {
                          console.log('切换到Tab:', tab.id);
                          setActiveTab(tab.id);
                        }}
                        className="px-3 py-1 text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded transition-colors duration-150"
                      >
                        激活
                      </button>
                    )}
                    {tab.canClose && (
                      <button
                        onClick={() => {
                          console.log('关闭Tab:', tab.id);
                          closeTab(tab.id);
                        }}
                        className="px-3 py-1 text-xs bg-red-200 hover:bg-red-300 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 rounded transition-colors duration-150"
                      >
                        关闭
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 返回主页 */}
      <div>
        <a
          href="/"
          className="inline-block px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors duration-150"
        >
          返回主页
        </a>
      </div>
    </div>
  );
} 