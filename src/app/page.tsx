'use client';

import React from 'react';
import useTabStore from '@/stores/useTabStore';
import TabContent from '@/components/TabContent';

import ClientOnly from '@/components/ClientOnly';

export default function Home() {
  return (
    <ClientOnly fallback={<LoadingFallback />}>
      <HomeContent />
    </ClientOnly>
  );
}

const LoadingFallback: React.FC = () => {
  return (
    <div className="h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">加载中...</p>
      </div>
    </div>
  );
};

const HomeContent: React.FC = () => {
  const { createConversationTab, createConfigurationTab, getActiveTab, tabs } = useTabStore();
  const activeTab = getActiveTab();

  // 如果有激活的Tab，显示Tab内容
  if (activeTab) {
    return (
      <div className="h-full overflow-hidden">
        <TabContent />
      </div>
    );
  }

  // 否则显示欢迎页面
  return (
    <div className="h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-2xl mx-auto text-center px-6">
        {/* 主标题 */}
        <div className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            MCP Client
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-2">
            Model Context Protocol 客户端
          </p>
          <p className="text-lg text-gray-500 dark:text-gray-400">
            智能AI助手，支持MCP集成和多模型对话
          </p>
        </div>

        {/* 功能特点 */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-6 h-6 text-blue-600 dark:text-blue-400"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              智能对话
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              支持多模型AI对话，智能上下文理解
            </p>
          </div>

          <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-6 h-6 text-green-600 dark:text-green-400"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              MCP集成
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              原生支持Model Context Protocol协议
            </p>
          </div>

          <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-6 h-6 text-purple-600 dark:text-purple-400"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              灵活配置
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              完全可定制的服务商和模型配置
            </p>
          </div>
        </div>

        {/* 快速开始按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => createConversationTab()}
            className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-150 shadow-sm hover:shadow-md"
          >
            开始对话
          </button>
          <button
            onClick={() => createConfigurationTab()}
            className="px-8 py-3 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium rounded-lg transition-colors duration-150 shadow-sm hover:shadow-md"
          >
            打开配置
          </button>
        </div>

        {/* 底部提示 */}
        <div className="mt-12 text-sm text-gray-500 dark:text-gray-400">
          <p>使用上方导航栏的 + 按钮可以快速创建新的对话或配置Tab</p>
        </div>
      </div>
    </div>
  );
}
