@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  /* 为整个应用添加圆角和阴影效果 */
  padding: 8px;
}

/* 应用窗口圆角样式 */
.app-window {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Tauri 桌面应用的圆角效果 */
.tauri-app {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 确保在 Tauri 环境下的圆角效果 */
@media (display-mode: standalone) {
  body {
    padding: 0;
    background: transparent;
  }

  .app-window {
    border-radius: 12px;
    margin: 8px;
    height: calc(100vh - 16px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }
}

/* 针对 Tauri 透明窗口的特殊处理 */
html[data-tauri] body {
  background: transparent;
  padding: 8px;
}

html[data-tauri] .app-window {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

html[data-tauri] .dark .app-window {
  background: rgb(17, 24, 39); /* gray-900 */
}
