{"name": "mcp-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "tauri": "tauri", "tauri-dev": "tauri dev", "tauri-build": "tauri build"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tauri-apps/api": "^2.5.0", "next": "15.3.2", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tauri-apps/cli": "^2.5.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}