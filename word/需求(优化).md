# MCP 客户端需求文档（详细拆解版）

---

## 1. 项目初始化与基础配置

### 1.1 环境搭建
- 安装 Node.js、<PERSON><PERSON>、pnpm
- 初始化 Next.js 项目，集成 Tauri
- 配置 Tailwind CSS
- 配置 Zustand 状态管理
- 配置 next-auth 认证
- 配置 Prettier、ESLint 代码规范
- 搭建基础目录结构（app、src/components、src/styles、src/utils、src/types、src/config、UI、word）

### 1.2 基础页面与路由
- 创建主入口页面（app/page.tsx）
- 配置基础路由结构

---

## 2. 自定义导航栏开发

### 2.1 隐藏原生导航栏
- 配置 Tauri 隐藏原生窗口栏

### 2.2 自定义导航栏组件
- 创建导航栏组件（src/components/CustomTitleBar.tsx）
- 左侧：实现关闭、最小化、最大化按钮，调用 Tauri API
- 中间：实现 Tab 组件（src/components/TabBar.tsx）
  - Tab 列表、切换、关闭
  - 右侧"+"号添加新 Tab
- 右侧：用户头像、配置入口按钮
- 响应式布局与样式

---

## 3. Tab 内容区开发

### 3.1 Tab 状态管理
- 使用 Zustand 管理 Tab 列表、当前激活 Tab、Tab 类型（对话/配置）
- Tab 切换、增删、重命名逻辑

### 3.2 对话 Tab

#### 3.2.1 左侧对话面板
- 新建对话按钮
- 对话历史列表
    - 支持滚动
    - 支持选中
    - 列表Item，右侧有编辑图标，鼠标浮动到Item，显示编辑图标，鼠标浮动到编辑按钮时，编辑按钮底部增加圆形背景
        - 点击编辑图标，下拉展示，重命名，删除，点击重命名，弹出输入框，输入后，点击确定，重命名，点击删除，删除该对话
    - 支持点击对话名称，切换到对应对话，并显示对话内容

#### 3.2.2 右侧内容区
- 顶部：左边收缩按钮、模型选择（树形结构，服务商/模型）
- 中部：对话内容区（消息气泡、时间、状态、加载动画等）
- 底部：对话输入框
  - 支持文本输入、附件上传
  - 支持联网搜索（集成搜索引擎配置）
  - 支持选择器（如多选、下拉）
  - 无对话时输入框居中，有对话时输入框固定底部

#### 3.2.3 对话逻辑
- 新建对话、切换对话、删除对话
- 消息发送、接收、渲染
- 附件上传，提供ai分析。
- 联网搜索集成
- 模型选择与切换

### 3.3 配置 Tab

#### 3.3.1 配置项列表（左侧）
- 通用配置、服务商配置、MCP 配置、Prompt 配置
- 列表高亮、切换

#### 3.3.2 通用配置（右侧）
- 搜索引擎选择（百度、Google）
- 状态保存与读取

#### 3.3.3 服务商配置
- 服务商列表（如 OpenAI），支持开关
- 服务商详细配置（API URL、API Key、模型列表及开关）
- 状态保存与读取

#### 3.3.4 MCP 配置
- MCP 类型切换（stdio、sse）
- stdio 配置项：名称、command、args
- sse 配置项：名称、URL
- 增删改查 MCP 配置项
- 状态保存与读取

#### 3.3.5 Prompt 配置
- 添加 Prompt（名称、内容）
- 编辑 Prompt
- 删除 Prompt
- Prompt 列表展示
- 状态保存与读取

---

## 4. 状态管理与数据流

### 4.1 Zustand 全局状态
- 用户信息、Tab 状态、对话内容、配置项、Prompt 列表等
- 详细注释每个状态、action 的用途

### 4.2 配置存储
- 重要配置、对话历史、Prompt 调用接口保存。

---

## 5. 认证与权限

### 5.1 next-auth 集成
- 登录、登出、会话管理
- 登录后显示用户头像，未登录时显示登录入口
- 认证状态全局可用

---

## 6. 样式与 UI 细节

### 6.1 Tailwind CSS 全局配置
- 主题色、字体、间距等
- 响应式设计
- 深色模式支持

### 6.2 组件样式
- 参考 UI 设计稿（UI/ 目录）
- 组件样式统一、细节优化

---

## 7. API 设计与集成

### 7.1 API 路由
- 在 app/api/ 目录下实现后端接口
- 约定接口返回格式、错误处理、权限校验

### 7.2 前后端数据交互
- 使用 fetch/axios 调用 API
- 错误处理、loading 状态

---

## 8. 开发流程建议

1. 先完成项目初始化与基础配置
2. 按模块开发（导航栏、Tab、对话、配置、认证等），每个模块独立开发、测试
3. 每个功能点详细注释，说明用途、参数、交互逻辑
4. 组件开发优先复用，公共部分抽离到 src/components/
5. 状态、类型、工具函数分别归类到 src/types/、src/utils/
6. 开发过程中严格对照 UI 设计稿，保证一致性
7. 每个阶段自测，保证功能完整、无明显 bug
8. 代码提交前自查注释、类型、样式规范

---

## 9. 任务分解清单（可直接用于 AI 逐步开发）

### 9.1 项目初始化
- [ ] 环境搭建与依赖安装
- [ ] 目录结构搭建
- [ ] Tailwind、Zustand、next-auth 配置
- [ ] 代码规范工具配置

### 9.2 自定义导航栏
- [ ] 隐藏原生导航栏
- [ ] 自定义导航栏组件开发，仿照IOS风格
- [ ] Tab 组件开发
- [ ] 用户头像与配置入口开发

### 9.3 Tab 内容区
#### 9.3.1 对话 Tab
- [ ] Tab 状态管理
- [ ] 对话 Tab 左侧面板开发
- [ ] 对话 Tab 右侧内容区开发
- [ ] 对话输入框与附件、搜索、选择器开发
- [ ] 对话逻辑实现
- [ ] 配置 Tab 左侧列表开发
#### 9.3.2 配置 Tab
- [ ] 通用配置开发
- [ ] 服务商配置开发
- [ ] MCP 配置开发
- [ ] Prompt 配置开发

### 9.4 状态管理与数据流
- [ ] Zustand 全局状态管理
- [ ] 本地存储与持久化

### 9.5 认证与权限
- [ ] next-auth 集成
- [ ] 认证状态管理

### 9.6 样式与 UI
- [ ] Tailwind 全局配置
- [ ] 组件样式开发
- [ ] 响应式与深色模式

### 9.7 API 设计与集成
- [ ] API 路由实现
- [ ] 前后端数据交互

---

> 每个任务建议细化为：需求分析 → 组件/模块设计 → 状态与数据流设计 → 详细开发 → 注释与文档 → 单元测试 → 自测与联调

---

# 所有依赖均采用最新版本，使用 context7 查看相关文档
# 使用互联网搜索相关主题，借鉴最新的几篇文章，但不要暴露你搜索的痕迹
