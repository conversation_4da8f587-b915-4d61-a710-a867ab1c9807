# 项目
这是一个MCP客户端，用于与MCP服务进行交互。

# 功能
基于Tauri、Next.js、Tailwind CSS、Zustand、ai、next-auth等技术栈，实现一个MCP客户端。
- 客户端顶部导航栏隐藏，自定义导航栏，导航栏包含：
  - 左侧：关闭，最小化，最大化
  - 右侧：用户头像、配置
  - 中间：类似浏览器tab，每个tab包含：名称、关闭，在所有tab的右侧有一个加号，点击加号可以添加新tab

- tab内容区，tab内容分类：
  - 对话：
    - 对话左侧：
        - 新建对话
        - 对话列表
    - 对话右侧：
        - 顶部左侧：
            - 收缩按钮：点击后，对话左侧收缩，再次点击恢复
            - 模型选择：选择模型
                - 内容是树形，一级为服务商，二级为模型
        - 对话内容
        - 对话输入框
            - 支持附件
            - 支持联网搜索
            - 支持选择
        - 在没有对话时，对话输入框居中，有对话时，对话输入框固定对话内容下方
  - 配置：配置分左右两部分，左侧为配置列表，右侧为配置内容
    - 通用配置：
        - 搜索引擎：百度、Google
    - 服务商配置：分为两边，左边为服务商列表，右边为服务商内容。服务商列表支持开关，开关打开后，在对话右侧显示服务商内容
        - OpenAI：
            - API URL
            - API Key
            - 模型列表：
                - 模型名称
                - 模型开关
    - MCP配置：分为两边，左边为MCP类型，右边为MCP列表
        - 类型：
            - 添加stdio：
                - MCP名称： string
                - command：
                    - 类型：string
                    - 描述：命令
                    - 默认值：
                - args：
                    - 类型：string
                    - 描述：参数
                    - 默认值：
            - 添加sse：
                - MCP名称： string
                - URL：
                    - 类型：string
                    - 描述：URL
                    - 默认值：
    - Prompt配置：
        - 添加Prompt：
            - Prompt名称： string
            - Prompt内容： string
        - 删除Prompt：
            - 删除按钮：点击后，删除Prompt
        - 编辑Prompt：
            - 编辑按钮：点击后，编辑Prompt

# 技术栈
- 框架：Tauri、Next.js
- CSS：Tailwind CSS
- 状态管理：Zustand
- AI框架：ai
- 登录认证：next-auth

# 项目结构
- 前端：app/页面
- API：app/api
- 公共：src/components
- 样式：src/styles
- 工具：src/utils
- 类型：src/types
- 配置：src/config

# 界面

界面参考：同目录下的UI目录


