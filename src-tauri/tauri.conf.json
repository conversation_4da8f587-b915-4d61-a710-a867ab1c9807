{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "mcp-client", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3000", "beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build"}, "app": {"windows": [{"title": "MCP Client", "width": 1200, "height": 800, "minWidth": 900, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": false, "transparent": true, "shadow": true}], "security": {"csp": null}, "macOSPrivateApi": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}